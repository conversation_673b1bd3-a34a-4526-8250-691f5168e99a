import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Button,
  Space,
  Card,
  Row,
  Col,
  message,
  Alert
} from 'antd';
import {
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons';

import {
  CrawlerConfigData,
  CrawlerConfigCreate,
  CrawlerConfigUpdate
} from '../../../services';

const { TextArea } = Input;

interface CrawlerConfigFormProps {
  configId?: string;
  initialData?: CrawlerConfigData;
  onSave: (data: CrawlerConfigCreate | CrawlerConfigUpdate) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const CrawlerConfigForm: React.FC<CrawlerConfigFormProps> = ({
  configId,
  initialData,
  onSave,
  onCancel,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  // 加载初始数据
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        name: initialData.config_name,
        description: initialData.description,
        tags: initialData.tags?.join(', '),
      });
    }
  }, [initialData, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      const values = await form.validateFields();

      // 转换表单数据为API格式
      const configData: any = {
        config_name: values.name as string,
        description: values.description || '',
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
      };

      await onSave(configData);
      message.success(configId ? '配置更新成功' : '配置创建成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
    >
      <Alert
        message="配置功能暂时简化"
        description="传统爬虫配置功能已移除，此页面仅保留基本配置功能。请使用新的爬取配置管理功能。"
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 基本信息 */}
      <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="配置名称"
              rules={[{ required: true, message: '请输入配置名称' }]}
            >
              <Input placeholder="请输入配置名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="tags"
              label="标签"
              tooltip="多个标签用逗号分隔"
            >
              <Input placeholder="标签1, 标签2" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="description"
          label="描述"
        >
          <TextArea rows={2} placeholder="请输入配置描述" />
        </Form.Item>
      </Card>

      {/* 操作按钮 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 24 }}>
        <div></div>
        <Space>
          <Button onClick={onCancel}>
            <CloseOutlined />
            取消
          </Button>
          <Button
            type="primary"
            loading={submitting}
            onClick={handleSubmit}
          >
            <SaveOutlined />
            {configId ? '更新配置' : '创建配置'}
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default CrawlerConfigForm;
